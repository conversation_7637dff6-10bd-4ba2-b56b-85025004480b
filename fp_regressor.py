import numpy as np
import pandas as pd
import os
import wandb
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import LinearRegression, Ridge, SGDRegressor
from scipy.stats import loguniform
from sklearn.tree import DecisionTreeRegressor
from sklearn.multioutput import MultiOutputRegressor
import matplotlib.pyplot as plt
from utils import *
import torch  # Added import for torch
from fp_config import regressor_config, sweeping

np.random.seed(33)
log_to_wandb = True  # Whether to log to Weights & Biases

# Use the combined configuration from fp_config.py
config = regressor_config

if config['model'] not in ["SGD", "LightGBM", "NGBoost"]:
    raise ValueError("Model not supported")

config['model_id'] = f"Plate{config['plate_serial']}_{config['target_cols']}_{config['model']}_{config['set_type'].capitalize()}_1in{config['dataset_fraction']}"

if config['model'] == "SGD":
    pass
elif config['model'] == "LightGBM":
    config['model_params'] = {
        "n_estimators" : 1000,
        "max_leaves" : 31,
        "learning_rate" : 0.05,
    }
elif config['model'] == "NGBoost":
    config['model_params'] = {
        # "kernel": "DecisionTree",
        "n_estimators": 500,
        "learning_rate": 0.01,
        "minibatch_frac": 1,
    }

# Import data
wandb.login()
run = wandb.init(
    project="force-plate",
    notes=f"Using {config['model']} model. Standard normalisation (input and output); {config['target_cols']}",
    config=config,
    save_code=log_to_wandb
    # mode="offline" if not log_to_wandb else "online")
)

raw_data_artifact = run.use_artifact(config['artifact_name'])

# Add dataset metadata to wandb run config
if 'dataset_id' in raw_data_artifact.metadata: run.tags = run.tags + (raw_data_artifact.metadata['dataset_id'],)
if 'capture_env' in raw_data_artifact.metadata: run.tags = run.tags + (raw_data_artifact.metadata['capture_env'],)
if 'num_loadcells' in raw_data_artifact.metadata: run.tags = run.tags + (f"{raw_data_artifact.metadata['num_loadcells']}-sensor",)
run.config.update(raw_data_artifact.metadata)

run_name = run.name
config = wandb.config
print(config)

x_scaler = StandardScaler()
y_scaler = StandardScaler()

# if need be, download the artifact
data_dir = 'artifacts/' + config.artifact_name
if not os.path.isdir(data_dir):  # check if data directory has already been downloaded
    data_dir = data_dir.replace(":", "-")
    if not os.path.isdir(data_dir):
        print("Current working directory: {0}".format(os.getcwd()))
        print("Couldn't find data. Downloading...")
        data_dir = raw_data_artifact.download()

device = torch.device('cpu')

train_set, test_set, validation_set, test_CoP, test_forces = make_datasets(
    data_dir,
    x_scaler,
    y_scaler,
    device,
    config.set_type,
    train_test_val_split=config.train_test_val_split,
    raw_data_artifact=raw_data_artifact,
    dataset_fraction=config.dataset_fraction,
    # ground_truth_columns=ground_truth_cols,
    # residual_type=config.residual_type,
    targets=config.target_cols,
    val_sample_grouplength=1,  # round(data_sample_rate/config.dataset_fraction),
    fz_threshold=False,
    fz_threshold_value=10,  # threshold value for force data
    down_sign=-1,  # pressing down results in positive values
    zero=True,  # must the loadcell readings be zeroed?
)

X_train = train_set.x.numpy()
Y_train = train_set.y.numpy()
X_val = validation_set.x.numpy()
Y_val = validation_set.y.numpy()
X_test = test_set.x.numpy()
Y_test = test_set.y.numpy()

fit_params = {}
if sweeping:
    if config['model'] == "LightGBM":
        fit_params['callbacks'] = [wandb.lightgbm.WandbCallback()]
    else:
        fit_params['callbacks'] = [wandb.sklearn.WandbCallback()]  # not sure if this will work for NGBoost
    params = {}

if config['model'] == "SGD":
    # reg = make_pipeline(SplineTransformer(n_knots=4, degree=3), Ridge(alpha=1e-3)).fit(X_train, Y_train)
    # reg = svm.SVR(kernel=config.kernel, C=1e3, gamma=0.1, verbose=True).fit(X_train[0:50000], Y_train[0:50000])

    params = {
        "estimator__loss": ["squared_error", "huber", "epsilon_insensitive", "squared_epsilon_insensitive"],
        "estimator__alpha": loguniform(1e-10, 1e0),  # regularisation strength
        "estimator__eta0": loguniform(1e-10, 1e0),  # initial learning rate
        "estimator__penalty": ["l1", "l2", "elasticnet"],
        "estimator__epsilon": loguniform(1e-5, 1e0),  # loss threshold for epsilon-insensitive
    }

    base_regressor = SGDRegressor(early_stopping=True, n_iter_no_change=20)
    reg = MultiOutputRegressor(base_regressor)

elif config['model'] == "LightGBM":
    import lightgbm as lgb

    if sweeping:
        params = {
            "n_estimators": np.logspace(1, 2, num=200, dtype=np.int32),  # loguniform(1e1, 1e4),
            # "num_leaves" : np.logspace(1,2,num=200,dtype=np.int32),#loguniform(1e1, 1e3),
            "learning_rate": loguniform(1e-3, 1e0)
        }
    else:
        params = config.model_params

    fit_params = {
        "eval_set": [(X_val, Y_val)],
        "callbacks": [lgb.early_stopping(stopping_rounds=10)]
        # , wandb.lightgbm.wandb_callback()]
    }

    reg = lgb.LGBMRegressor(**params)  # num_leaves=config.max_leaves)

elif config['model'] == "NGBoost":
    from ngboost import NGBRegressor

    # polynomial model
    # interaction = SplineTransformer(degree=2, n_knots=3) # PolynomialFeatures(degree = 3) # spline
    # X_train = interaction.fit_transform(X_train)
    # X_val = interaction.transform(X_val)
    # X_test = interaction.transform(X_test)

    fit_params = {
        "X_val": X_val,
        "Y_val": Y_val
    }

    params = config.model_params

    learner = DecisionTreeRegressor(max_depth=10, min_samples_leaf=10)
    # learner = LinearRegression()
    print(config.model_params['n_estimators'])
    reg = MultiOutputRegressor(NGBRegressor(Base=learner, **params, verbose_eval=20))

if sweeping:
    # Have to explicitly require this experimental feature
    from sklearn.experimental import enable_halving_search_cv  # noqa
    from sklearn.model_selection import HalvingRandomSearchCV
    from sklearn.model_selection import GridSearchCV

    reg = HalvingRandomSearchCV(
        reg,
        param_distributions=params,
        n_candidates=100,
        scoring='neg_mean_absolute_error',
        min_resources='exhaust',
        verbose=1
    ).fit(X_train, Y_train, **fit_params)
    print(reg.best_params_)
else:
    # .fit(X_train, Y_train.reshape(-1,), X_val, Y_val.reshape(-1,))
    reg.fit(X_train, Y_train, **fit_params)

# Log feature importance plot and upload model checkpoint to W&B
# if config.model == "LightGBM": wandb.lightgbm.log_summary(reg, save_model_checkpoint=True)

Y_test_pred = reg.predict(X_test)
num_inputs = X_test.shape[1]

if (not test_CoP is None):
    test_CoP = pd.DataFrame(test_CoP, columns=['Cx', 'Cy'])
base_save_path = os.path.join(wandb.run.dir, run_name)
if config.scale_in_model:
    reg_scaled = combine_model_and_scalers(reg, x_scaler, y_scaler, num_inputs, len(config.target_cols))
    save_model(reg_scaled, x_scaler, y_scaler, X_train.shape[1], config.target_cols, base_save_path)
else:
    save_model(reg, x_scaler, y_scaler, X_train.shape[1], config.target_cols, base_save_path)
# quantize_model_and_save(reg_scaled, x_scaler, y_scaler, config.target_cols, base_save_path, train_loader, config.scale_in_model)
# compare_model(Y_test_pred.reshape(-1,1), Y_test.reshape(-1,1), test_CoP, y_scaler, target_cols=config.target_cols, plot=False)

Y_test = y_scaler.inverse_transform(Y_test)
Y_test_pred = y_scaler.inverse_transform(Y_test_pred)
Y_train = y_scaler.inverse_transform(Y_train)
Y_train_pred = y_scaler.inverse_transform(reg.predict(X_train))

test_CoP = pd.DataFrame(test_CoP, index=None, columns=['Cx', 'Cy'])
test_forces = pd.DataFrame(test_forces, index=None, columns=['Fx', 'Fy', 'Fz'])

plate_size = (config.plate_dim_x, config.plate_dim_y)

analyse_model_perf(
    Y_test_pred,
    Y_test,
    test_CoP,
    test_forces,
    target_cols=config.target_cols,
    plate_size=plate_size,
    log_to_wandb=True,
    plot=not sweeping,
    threshold=config.fz_analysis_threshold_value,
    base_save_path=base_save_path
)