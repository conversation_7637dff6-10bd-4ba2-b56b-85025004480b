import torch
from torch.utils.data import Dataset, DataLoader
import numpy as np
# Add NaN as an alias to nan for backward compatibility with code expecting uppercase NaN
if not hasattr(np, 'NaN'):
    np.NaN = np.nan
import math
import os
from tqdm import tqdm
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.base import BaseEstimator
from sklearn.linear_model import SGDRegressor
from sklearn.multioutput import MultiOutputRegressor
from sklearn.metrics import r2_score, mean_absolute_percentage_error, mean_squared_error
import wandb
import plotly.express as px
import plotly.offline as py_offline
import matplotlib.pyplot as plt
# %matplotlib widget
# plt.ion()
# plt.show(block=False)
# plt.use("QtAgg")
# plt.switch_backend('ipympl')
#%pip install joblib
import joblib
from ngboost import NGBRegressor
from lightgbm import LGBMRegressor
import onnx
from onnxruntime.quantization import QuantFormat, QuantType, StaticQuantConfig, quantize as ort_quantize, CalibrationMethod
from onnxruntime.quantization import CalibrationDataReader
from onnxruntime.quantization.shape_inference import quant_pre_process
from collections import OrderedDict

import torch
from torch.utils.data import Dataset

class PlateDataSet(Dataset):
    def __init__(self, X, Y, array_type="torch", device=None, sequence_length=None):
        if device is None and array_type == "torch":
            raise ValueError("Device must be provided if array_type is torch")

        if array_type == "torch":
            self.x = torch.Tensor(X).to(device)
            self.y = torch.Tensor(Y).to(device)
        elif array_type == "numpy":
            self.x = X
            self.y = Y
        self.n_samples = self.x.shape[0]
        self.sequence_length = sequence_length

    def __getitem__(self, index):
        if self.sequence_length:
            if isinstance(index, slice):
                start = index.start or 0
                stop = index.stop or self.n_samples
                step = index.step or 1
                indices = range(start, stop, step)
                sequences_x = []
                sequences_y = []
                for idx in indices:
                    if idx + self.sequence_length > self.n_samples:
                        raise IndexError("Index out of range for sequence length")
                    sequences_x.append(self.x[idx:idx + self.sequence_length])
                    sequences_y.append(self.y[idx + self.sequence_length - 1])
                return torch.stack(sequences_x), torch.stack(sequences_y)
            else:
                if index + self.sequence_length > self.n_samples:
                    raise IndexError("Index out of range for sequence length")
                return (self.x[index:index + self.sequence_length],
                        self.y[index + self.sequence_length - 1])
        else:
            return self.x[index], self.y[index]

    def __len__(self):
        if self.sequence_length:
            return self.n_samples - self.sequence_length + 1
        else:
            return self.n_samples

def precalculate_physics(X, physics_informed_type):
    if physics_informed_type == "PINN4" or physics_informed_type == "PINN5":
        # PINN 4 / 5 calculation of Cy
        Fm2 = X[:,[1]] * 981 / 4.95
        Fm4 = X[:,[3]] * 981 / 4.95
        Fz_calc = np.minimum(0, np.maximum(-2000, 2*(Fm2 + Fm4)))
        Cy_calc = np.minimum(225, np.maximum(-225, -200*(Fm2-Fm4)/(Fm2+Fm4) * (Fz_calc/-1000) ))
        X = np.hstack((X, Cy_calc))

    return X

def inject_zero_frames(data, loadcell_cols, ground_truth_columns, zero_frame_percentage=0.05):
    """
    Inject zero frames into the dataset where loadcell readings and target values are all 0.

    Args:
        data: pandas DataFrame containing the data
        loadcell_cols: list of loadcell column names
        ground_truth_columns: list of ground truth column names
        zero_frame_percentage: percentage of total data that should be zero frames (default: 0.05 = 5%)

    Returns:
        pandas DataFrame with zero frames injected
    """
    current_length = len(data)
    # Calculate how many zero frames to add to make them 5% of the total
    # If we want zero frames to be 5% of total, and we have N existing frames,
    # then: zero_frames / (N + zero_frames) = 0.05
    # Solving: zero_frames = 0.05 * N / (1 - 0.05) = 0.05 * N / 0.95
    num_zero_frames = int(current_length * zero_frame_percentage / (1 - zero_frame_percentage))

    if num_zero_frames == 0:
        print("No zero frames to inject (dataset too small)")
        return data

    print(f"Injecting {num_zero_frames} zero frames ({zero_frame_percentage*100:.1f}% of total data)")

    # Create zero frames with the same structure as the original data
    zero_frames = pd.DataFrame(0, index=range(num_zero_frames), columns=data.columns)

    # Set all loadcell readings to 0 (they should already be 0, but being explicit)
    zero_frames[loadcell_cols] = 0

    # Set all ground truth values to 0
    zero_frames[ground_truth_columns] = 0

    # Concatenate the zero frames with the original data and shuffle
    combined_data = pd.concat([data, zero_frames], ignore_index=True)

    # Shuffle the data to distribute zero frames throughout
    combined_data = combined_data.sample(frac=1, random_state=42).reset_index(drop=True)

    return combined_data

def split_data(X,Y,test_size,random_state=33, min_test_portion_length=1):
    if min_test_portion_length == 1:
        return train_test_split(X,Y,test_size=test_size,random_state=random_state)
    else: # need to make contiguous windows of data at random spots, more than min_test_portion_length apart
        extraneous_samples = len(X) % min_test_portion_length
        used_length = len(X) - extraneous_samples
        X_unused, Y_unused = np.array(X[used_length:]), np.array(Y[used_length:])
        split_count = int(used_length / min_test_portion_length)
        test_window_count = math.floor(split_count * test_size)
        train_window_count = split_count - test_window_count

        if test_window_count == 0:
            raise ValueError("Test size too small for provided <min_test_portion_length> and size of X & Y. Try increasing <test_size> or using a smaller <min_test_portion_length>.")

        X_windows = np.split(X[:used_length], split_count)
        Y_windows = np.split(Y[:used_length], split_count)

        # use the first test_size % of randomly-shuffled windows to make the test set
        np.random.seed(random_state)
        permutation = np.random.permutation(len(X_windows))
        test_window_indices = permutation[:test_window_count]
        train_window_indices = permutation[test_window_count:]

        X_test_windows, Y_test_windows = np.array(X_windows)[test_window_indices], np.array(Y_windows)[test_window_indices]
        X_train_windows, Y_train_windows = np.array(X_windows)[train_window_indices], np.array(Y_windows)[train_window_indices]

        # recombine the selected windows into one set
        X_test, Y_test = np.concatenate(X_test_windows, axis=0), np.concatenate(Y_test_windows, axis=0)
        X_train, Y_train = np.concatenate(X_train_windows, axis=0), np.concatenate(Y_train_windows, axis=0)

        # add the unused samples to the end of the training set
        if len(X_unused) > 0:
            print(len(X_unused))
            print(len(X_train))
            X_train, Y_train = np.concatenate((X_train, X_unused)), np.concatenate((Y_train, Y_unused))

        return X_train, X_test, Y_train, Y_test

def make_datasets(directory,
        x_scaler:StandardScaler,
        y_scaler:StandardScaler,
        device,
        set_type,
        train_test_val_split=[0.8,0.1,0.1],
        raw_data_artifact=None,
        physics_informed_type=None,
        engineered_features=None,
        dataset_fraction=None, # use every n samples
        val_sample_grouplength=10000, # shortest contiguous section of data used in making validation & test sets
        ground_truth_columns=['Fx','Fy','Fz','Cy','Cx'],
        targets=['Fx','Fy','Fz'],
        scale=True,
        fz_threshold=True,
        fz_threshold_value=10, # threshold value for force data
        down_sign=1, # pressing down results in positive values
        zero=False, # must the loadcell readings be zeroed?
        zero_frame_percentage=0.05, # percentage of total data that should be zero frames
        files_to_ignore=[],
        test_set_files=[],
        sequence_length=None,
        window_size=10,
        window_stride=1
    ) -> tuple([Dataset, Dataset]):

    data = []
    test_data = [] if len(test_set_files) > 0 else None
    dataFiles = []
    has_cop = ('Cx' in ground_truth_columns) and ('Cy' in ground_truth_columns)

    for root, dirs, files in os.walk(directory): # recursive
        for file in files: #append the file name to the list
            dataFiles.append(os.path.join(root,file))

    count = 0
    # Iterate over all files in directory
    for i,dataFilename in tqdm(enumerate(dataFiles)):
        filenameKernal, fileExtension = os.path.splitext(dataFilename)
        fileWithPath = dataFilename # directory + "/" + dataFilename
        # if filenameKernal.endswith("AMTI vs W01 & W02 v2 Hop 1,2"): continue # leave out to test with
        if fileExtension == ".csv":
            if os.path.basename(dataFilename) in files_to_ignore: continue
            count += 1
            newDF = pd.read_csv(fileWithPath, index_col=0, skiprows=[1], dtype=np.float64)
            # newDF = pd.read_csv(directory + "/" + dataFilename, skiprows=1000, names=['Fx', 'Fy', 'Fz', 'Cx', 'Cy', 'Loadcell_1', 'Loadcell_2', 'Loadcell_3', 'Loadcell_4', 'Loadcell_5', 'Loadcell_6', 'Loadcell_7', 'Loadcell_8', 'Temp'])
            newDF.reset_index(inplace=True, drop=True)

            # Sim: Time-align inputs and outputs - input 10ms (1 sample at 100Hz) ahead of loadcells
            if set_type == "sim":
                loadcell_col_mask = ~(newDF.columns.isin(ground_truth_columns)) # get all the loadcell columns - however many there are
                loadcell_cols = newDF.columns[loadcell_col_mask]
                newDF[loadcell_cols] = newDF[loadcell_cols].shift(-1) # shift loadcell data up by one sample
                newDF = newDF.drop(newDF.index[-1], axis=0) # drop last row

            if os.path.basename(dataFilename) in test_set_files:
                test_data.append(newDF)
                continue

            if test_data is not None:
                test_data = test_data.dropna()
                test_data['test_set'] = 1
                newDF['test_set'] = 0
                newDF = pd.concat([newDF, test_data], ignore_index=True)

            # Drop unused columns
            for to_drop in ['temp', 'temperature', 'Temp', 'Temperature', 'piston', 'Piston']:
                if to_drop in newDF.columns:
                    newDF = newDF.drop(to_drop, axis=1)
                    print("Dropped column: " + to_drop)

            # Drop extra loadcell columns if they exist
            if 'test_set' in newDF.columns:
                loadcell_col_mask = ~(newDF.columns.isin(ground_truth_columns + ['test_set']))
            else:
                loadcell_col_mask = ~(newDF.columns.isin(ground_truth_columns))
            loadcell_cols = newDF.columns[loadcell_col_mask]
            if raw_data_artifact is not None and 'num_loadcells' in raw_data_artifact.metadata: # more loadcells recorded than used
                num_loadcells = raw_data_artifact.metadata['num_loadcells']
                extra_loadcell_cols = loadcell_cols[num_loadcells:]
                loadcell_cols = loadcell_cols[:num_loadcells]
                newDF = newDF.drop(extra_loadcell_cols, axis=1)

            # zero the loadcell readings based on the first 1000ms
            zero_point = newDF[loadcell_cols].iloc[:10].mean()
            if engineered_features is not None and 'zero_point' in engineered_features:
                # add columns for zero point of each loadcell
                for col in loadcell_cols:
                    newDF[col + "_zero_point"] = zero_point[col]

            if zero:
                newDF[loadcell_cols] = newDF[loadcell_cols] - zero_point

            if engineered_features is not None and 'diff' in engineered_features:
                # add columns for difference between each loadcell and its previous value
                for col in loadcell_cols:
                    newDF[col + "_diff"] = newDF[col].diff().fillna(0)

            if engineered_features is not None and 'window' in engineered_features:
                # add columns for past frames of each loadcell's history
                for col in loadcell_cols:
                    for i in range(window_size, len(newDF)):
                        newDF[col + f"_frame_-{i}"] = newDF[col].iloc[i-window_size:i].values

            if dataset_fraction:
                data.append(newDF.iloc[::dataset_fraction])
            else:
                data.append(newDF)

    # input_cols = [col for col in newDF.columns if col not in ground_truth_columns]
    # plt.plot(newDF[input_cols])
    # plt.legend(input_cols)
    # plt.show()

    # concatenate DFs
    data = pd.concat(data, ignore_index=True)

    # prep for real-life data
    data = data.dropna()

    # Inject zero frames right after zeroing step
    if zero_frame_percentage > 0:
        if 'test_set' in data.columns:
            loadcell_col_mask = ~(data.columns.isin(ground_truth_columns + ['test_set']))
        else:
            loadcell_col_mask = ~(data.columns.isin(ground_truth_columns))
        loadcell_cols = data.columns[loadcell_col_mask]
        data = inject_zero_frames(data, loadcell_cols, ground_truth_columns, zero_frame_percentage=zero_frame_percentage)

    # remove any rows where Fz isn't a true reading i.e. -10 when pressing down should be positive
    if fz_threshold:
        data = data[down_sign*data['Fz'] >= down_sign*fz_threshold_value]

    # Check all is well...
    print("Data before scaling, after fraction application:")
    print(data.head)

    # Sim: Time-align inputs and outputs - input 10ms (1 sample at 100Hz) ahead of loadcells
    if set_type == "sim":
        data[loadcell_cols] = data[loadcell_cols].shift(-1) # shift loadcell data up by one sample
        data = data.drop(data.index[-1], axis=0) # drop last row

    # Split off test set if one is provided
    if test_data is not None:
        test_data = pd.concat(test_data, ignore_index=True)
        test_data = data[data['test_set'] == 1]
        data = data[data['test_set'] == 0]
        test_data = test_data.drop('test_set', axis=1)
        data = data.drop('test_set', axis=1)
        print("Test data:")
        print(test_data.head)
        X_test_predefined = test_data.drop(ground_truth_columns,axis=1).values
        Y_test_predefined = test_data[targets].values
        if has_cop: Y_test_predefined = np.hstack((Y_test_predefined, test_data[['Cx','Cy']].values))
        Y_test_predefined = np.hstack((Y_test_predefined, test_data[['Fx','Fy','Fz']].values))

    # Separate out inputs and targets
    X = data.drop(ground_truth_columns,axis=1).values # inputs
    if physics_informed_type is not None:
        X = precalculate_physics(X, physics_informed_type)

    if 'Mx' in targets and 'My' in targets and ('Mx' not in ground_truth_columns and 'My' not in ground_truth_columns):
        # need to calculate Mx and My from CoP and force
        data['Mx'] = data['Cy'] * data['Fz']
        data['My'] = - data['Cx'] * data['Fz']

    Y = data[targets].values # targets

    # Append real forces and CoPs to Y before train_test_split so we can pull them out afterwards
    # They may already be in, but this ensures they are when the a limited set of targets is used
    if has_cop: Y = np.hstack((Y, data[['Cx','Cy']].values))
    Y = np.hstack((Y, data[['Fx','Fy','Fz']].values))

    # Make train / test / val split (0.8,0.1,0.1)
    if dataset_fraction and val_sample_grouplength > 1:
        min_test_portion_length = int(val_sample_grouplength / dataset_fraction)
    else:
        min_test_portion_length = val_sample_grouplength

    train_split = train_test_val_split[0]
    test_split = train_test_val_split[1]
    val_split = train_test_val_split[2]
    val_of_train_split = val_split / (train_split - test_split)

    X_train, X_test, Y_train, Y_test = split_data(X,Y,test_size=test_split,random_state=33, min_test_portion_length=min_test_portion_length)
    X_train, X_val, Y_train, Y_val = split_data(X_train,Y_train,test_size=val_of_train_split,random_state=33,min_test_portion_length=min_test_portion_length)

    # If test set is provided, override the test set split
    if test_data is not None:
        X_test = X_test_predefined
        Y_test = Y_test_predefined

    total_samples = len(X_train) + len(X_test) + len(X_val)
    data_ratios = [len(X_train)/total_samples, len(X_test)/total_samples, len(X_val)/total_samples]
    print(f"Ratios post-splitting (train:test:val): {data_ratios}")

    # Pull CoPs out of the Y sets again now that they've been split for train and test
    Y_train = np.hsplit(Y_train, [len(targets)])[0]
    if has_cop:
        Y_test, test_CoP, test_forces = np.hsplit(Y_test, [len(targets), len(targets)+2])
    else:
        test_CoP = None
        Y_test, test_forces = np.hsplit(Y_test, [len(targets)])
    Y_val = np.hsplit(Y_val, [len(targets)])[0]

    # Find means and standard deviations of the training features and apply them to all sets
    if (scale):
        X_train = x_scaler.fit_transform(X_train) # find and apply scaling transform
        X_val, X_test = x_scaler.transform(X_val), x_scaler.transform(X_test) # just apply the transform
        Y_train = y_scaler.fit_transform(Y_train) # find and apply scaling transform
        Y_val, Y_test = y_scaler.transform(Y_val), y_scaler.transform(Y_test) # just apply the transform

    print(f"X-scaler: Means {x_scaler.mean_}, Scales: {x_scaler.scale_}")
    print(f"Y-scaler: Means {y_scaler.mean_}, Scales: {y_scaler.scale_}")

    # http://www.faqs.org/faqs/ai-faq/neural-nets/part2/ - search Should I standardize the target variables (column vectors)?

    # If the distribution of the quantity is normal, then it should be standardized, otherwise, the data should be normalized. This applies if the range of quantity values is large (10s, 100s, etc.) or small (0.01, 0.0001).
    # https://machinelearningmastery.com/standardscaler-and-minmaxscaler-transforms-in-python/

    # Make into datasets that Pytorch can use
    train_set = PlateDataSet(X_train, Y_train, device=device, sequence_length=sequence_length)
    validation_set = PlateDataSet(X_val, Y_val, device=device, sequence_length=sequence_length)
    test_set = PlateDataSet(X_test, Y_test, device=device, sequence_length=sequence_length)

    return train_set, test_set, validation_set, test_CoP, test_forces

def fit_scaler(sc):
    d=[[1,2,3],[1,2,3],[1,2,3],[1,2,3]]
    sc.fit(d)

if __name__ == "__main__":
    X = np.round(np.linspace(0,99,400).reshape(100,4))
    Y = np.round(np.linspace(1000,1099,300).reshape(100,3))
    # # X = pd.DataFrame(x, columns=["LC_1","LC_2","LC_3","LC_4"])
    # # Y = pd.DataFrame(y, columns=["Fx","Fy","Fz"])
    X_train, X_test, Y_train, Y_test = split_data(X,Y,test_size=0.2,random_state=33, min_test_portion_length=5)

    dataset = PlateDataSet(X, Y)

    # get the first sample and unpack
    features, labels = dataset[0]
    print(features, labels)

    train_set = PlateDataSet(X_train, Y_train)
    test_set = PlateDataSet(X_test, Y_test)

    train_loader = DataLoader(train_set, batch_size=10, shuffle=True)
    test_loader = DataLoader(test_set, batch_size=10, shuffle=True)

    # print(test_loader.dataset.x)

    for test_features, test_labels in iter(test_loader):
        print(test_features)

    sc = StandardScaler()
    fit_scaler(sc)
    print(sc.mean_)

def train(model, train_loader:DataLoader, val_loader:DataLoader, criterion, optimizer, scheduler, config, device):
    """
    ## Define Training Logic

    `wandb.watch` will log the gradients and the parameters of the model to W&B,
    every `log_freq` steps of training.

    Standard training routine: iterate over epochs and batches,
    running forward and backward passes and applying our `optimizer`.
    """
    train_losses = []
    validation_losses = []
    X_val, Y_val = val_loader.dataset.x, val_loader.dataset.y

    print("Starting training")

    # Track model evolution with wandb
    if False: #config.physics_informed_type:
        wandb.watch(model.net, criterion, log="all", log_freq=10) # TODO: should .net be used for residual models?
    else:
        wandb.watch(model, criterion, log="all", log_freq=10)

    # Run training and track with wandb
    example_count = 0  # number of examples seen
    # perm_generator = torch.Generator(device)
    for epoch in tqdm(range(config.epochs)):
        batch_loss = 0
        batch_count = 0
        model.train()
        for batch_X, batch_Y in iter(train_loader):
            batch_loss += train_batch(batch_X.to(device), batch_Y.to(device), model, optimizer, scheduler, criterion).detach().cpu()
            batch_count += 1
            del batch_X, batch_Y
            torch.cuda.empty_cache()

        train_loss = batch_loss / batch_count # make average of the mini-batches trained
        train_losses.append(train_loss)

        val_loss = validate(model, val_loader, criterion, device)
        validation_losses.append(val_loss)

        wandb.log({"training_loss": train_loss, "validation_loss": val_loss})

        example_count += len(train_loader.dataset.x)

        # Report metrics every 5 epochs
        if ((epoch + 1) % 5) == 0:
            train_log(train_loss, example_count, epoch)

    plot_losses(config.epochs, train_losses, validation_losses)

    return train_loss, val_loss

def train_batch(X, Y, model, optimizer, scheduler, criterion):
    # Forward pass ➡️
    Y_pred = model(X)
    loss = criterion(Y_pred, Y, X) * 10

    # Backward pass ⬅️
    optimizer.zero_grad()
    loss.backward()

    # Step with optimizer ↘️
    optimizer.step()
    scheduler.step()

    return loss

def validate(model, val_loader, criterion, device):
    model.eval()
    val_loss = 0
    val_count = 0
    with torch.no_grad():
        for batch_X, batch_Y in val_loader:
            batch_X, batch_Y = batch_X.to(device), batch_Y.to(device)
            Y_val_pred = model(batch_X)
            loss = criterion(Y_val_pred, batch_Y, batch_X).cpu().item() * 10
            val_loss += loss
            val_count += 1

    return val_loss / val_count

def train_log(loss, example_count, epoch):
    loss = float(loss)

    wandb.log({"epoch": epoch, "loss": loss}, step=example_count)
    print(f"Loss after " + str(example_count).zfill(5) + f" examples: {loss:.3f}")

def plot_losses(epochs, train_losses, validation_losses):
    losses = pd.DataFrame(np.transpose(np.array([train_losses, validation_losses])), columns=["train", "val"])
    losses = losses.reset_index()

    loss_df_melt = losses.melt(id_vars='index', value_vars=["train", "val"])
    fig = px.line(data_frame=loss_df_melt, x='index', y='value', width=1000, height=500 , color='variable',
                labels={
                    'index': "Epoch",
                    'value': "Loss"
                    },
                title="Training Losses: Training vs Validation Set")

    fig.show()

def append_config_details(config:dict):
    extra_config = {}
    if config['plate'] == 'W01' and config['set_type'] == 'rig':
        extra_config['artifact_name'] = 'plate5-real:v6'
        extra_config['dataset_description'] = "20211125 - Rig; Plate W01 (v5.1 Watson 450mm); 1000Hz; Filtered; Zeros Removed; Cropped; No test/train split"
        extra_config['dataset_id'] = "platew01_set4_2021-11-25"
    elif config['plate'] == 'W01,2' and config['set_type'] == 'locklab':
        extra_config['artifact_name'] = 'plate5-real:v5'
        extra_config['dataset_description'] = "20211125 - LockLab; Plate W01 & W02 vs AMTI v2 (v5.1 Watson 450mm); 1000Hz; Filtered; No test/train split"
        extra_config['dataset_id'] = "platew01,2_amti_v2_2021-11-25"
    elif config['plate'] == '4.44.6' and config['set_type'] == 'sim':
        extra_config['artifact_name'] = 'simulated-raw:v116'
        extra_config['dataset_description'] = "20220505 - Simulator; Plate V6.05.01 (Vader 500mm); 2000x 10s 500-pos 500-force runs; 100Hz; No test/train split"
        extra_config['dataset_id'] = "platev6.05_simv1_set1"

    return {**config, **extra_config}

# ----------  interval helpers  ----------

def split_interval_preds(Y_pred: np.ndarray, target_cols):
    """
    If Y_pred has 2·d columns, treat the first d as lower bounds L and
    the next d as upper bounds U.  Returns (is_interval, L, U, MID).
    """
    d = len(target_cols)
    if Y_pred.shape[1] == 2 * d:
        L  = Y_pred[:, :d]
        U  = Y_pred[:, d:]
        MID = 0.5 * (L + U)
        return True, L, U, MID
    else:
        return False, None, None, Y_pred

def interval_metrics(L, U, Y_true, axis_names):
    """
    Compute per-axis coverage and mean width.
    Returns two dicts keyed by axis name.
    """
    cover_dict, width_dict = {}, {}
    for i, col in enumerate(axis_names):
        cover = np.mean((Y_true[:, i] >= L[:, i]) & (Y_true[:, i] <= U[:, i]))
        width = np.mean(U[:, i] - L[:, i])
        cover_dict[col] = cover
        width_dict[col] = width
    return cover_dict, width_dict

def log_interval_to_wandb(coverage, width, prefix=""):
    """Tiny helper so the WandB logging clutter stays out of the main code."""
    if not wandb.run:
        return
    for k, v in coverage.items():
        wandb.log({f"{prefix}{k}_interval_coverage": v})
    for k, v in width.items():
        wandb.log({f"{prefix}{k}_interval_mean_width": v})

def pretty_interval_report(coverage, width, unit_dict):
    lines = ["\nINTERVAL DIAGNOSTICS (95 % target)\n" + "-" * 34]
    for k in coverage.keys():
        u = unit_dict.get(k, "")
        lines.append(
            f"{k:>3}:  coverage = {coverage[k]*100:5.2f}%   "
            f"⟨U-L⟩ = {width[k]:.2f} {u}"
        )
    return "\n".join(lines)

def is_interval_pred(pred, d):           # True if 2·d columns
    return pred.shape[1] == 2 * d

def split_LUM(pred, d):
    """Returns (L, U, MID) where pred shape is (N, 2d)."""
    L   = pred[:, :d]
    U   = pred[:, d:]
    MID = 0.5 * (L + U)
    return L, U, MID

# ───────────────── interval-aware scaler helpers ─────────────────
def scaler_transform_interval(arr, scaler):
    """
    arr  : (N, 2·d)  – lower-then-upper
    Returns (N, 2·d) in the *scaled* space.
    """
    d = len(scaler.mean_)
    lower = scaler.transform(arr[:, :d])
    upper = scaler.transform(arr[:, d:])
    return np.concatenate([lower, upper], axis=1)

def scaler_inverse_interval(arr, scaler):
    """
    arr  : (N, 2·d)  – lower-then-upper
    Returns (N, 2·d) back in the *original* space.
    """
    d = len(scaler.mean_)
    lower = scaler.inverse_transform(arr[:, :d])
    upper = scaler.inverse_transform(arr[:, d:])
    return np.concatenate([lower, upper], axis=1)
# ────────────────────────────────────────────────────────────────

def score(pred, target):
    '''Calculates the average CoP distance error and force error -
    this to keep a consistent metric of how well the model is performing regardless of
    what loss function / criterion we use.'''
    # Mean distance error for centre of pressure
    cop_cols = [col for col in target.columns if 'C' in col]
    if len(cop_cols) > 0:
        return np.mean(np.abs(pred[cop_cols] - target[cop_cols]).to_numpy())

    # Mean absolute error for force
    force_cols = [col for col in target.columns if 'F' in col]
    if len(force_cols) > 0:
        return np.mean(np.abs(pred[force_cols] - target[force_cols]).to_numpy())

    return 0

def analyse_model_perf(Y_pred, Y_true, Y_CoPs: pd.DataFrame, Y_forces: pd.DataFrame, target_cols, plate_size=(500,500), log_to_wandb=True, plot=True, threshold=10, base_save_path=None):
    '''Previously compare_model'''

    target_cols = list(target_cols) # make a mutable copy

    # ── 1. tensor → numpy  ──────────────────────────────────────────
    Y_pred_np = Y_pred.detach().cpu().numpy() if torch.is_tensor(Y_pred) else np.asarray(Y_pred)
    Y_true_np = Y_true.detach().cpu().numpy() if torch.is_tensor(Y_true) else np.asarray(Y_true)

    # ── 2. detect interval output & split if present ───────────────
    d = len(target_cols)
    interval_mode = (Y_pred_np.shape[1] == 2 * d)

    if interval_mode:
        L_pred = Y_pred_np[:, :d]
        U_pred = Y_pred_np[:, d:]
        Y_mid  = 0.5 * (L_pred + U_pred)
        pred_mat = Y_mid                               # use mid-points for point metrics
    else:
        L_pred = U_pred = None                         # keep names defined
        pred_mat = Y_pred_np

    # ── 3. quick DataFrames (all subsequent code uses these) ───────
    pred_df = pd.DataFrame(pred_mat, columns=target_cols)
    true_df = pd.DataFrame(Y_true_np, columns=target_cols)

    globals()['_TARGET_ORDER'] = list(target_cols)   # save original  order

    print(f"Number of datapoints before removing values of interest below thresh: {len(pred_df.iloc[:,0])}")

    # drop rows where |Fz| ≤ threshold
    keep_mask = abs(Y_forces['Fz']) > threshold
    pred_df   = pred_df[keep_mask]
    true_df   = true_df[keep_mask]
    Y_CoPs    = Y_CoPs[keep_mask]
    Y_forces  = Y_forces[keep_mask]

    if interval_mode:
        L_pred = L_pred[keep_mask]
        U_pred = U_pred[keep_mask]

        cov_dict, width_dict = interval_metrics(L_pred, U_pred,
                                                true_df.to_numpy(),
                                                target_cols)
        interval_cov_dict   = cov_dict   # local, no globals
        interval_width_dict = width_dict
        print(pretty_interval_report(cov_dict, width_dict,
              {c: ('N' if 'F' in c else 'mm') for c in target_cols}))
        log_interval_to_wandb(cov_dict, width_dict)

    print(f"Number of datapoints after removing values of interest below {threshold} thresh: {len(pred_df.iloc[:,0])}")


    if 'Mx' in target_cols and 'My' in target_cols:
        # calculate Cx & Cy from Mx & My
        true_df['Cx'] = -true_df['My'] / true_df['Fz']
        true_df['Cy'] =  true_df['Mx'] / true_df['Fz']

        pred_df['Cx'] = -pred_df['My'] / pred_df['Fz']
        pred_df['Cy'] =  pred_df['Mx'] / pred_df['Fz']

        mask_fz_thresh = pred_df['Fz'] < 50
        pred_df.loc[mask_fz_thresh, ['Cx', 'Cy']] = 0
        pred_df['Cx'] = pred_df['Cx'].clip(-plate_size[0]/2, plate_size[0]/2)
        pred_df['Cy'] = pred_df['Cy'].clip(-plate_size[1]/2, plate_size[1]/2)

        target_cols.extend(['Cx', 'Cy'])

    plt.scatter(Y_CoPs['Cx'], Y_CoPs['Cy'])

    datapoints = len(pred_df.iloc[:,0])
    force_cols = [col for col in target_cols if 'F' in col]
    if len(force_cols) > 0:
        force_score = score(pred_df.loc[:,force_cols], true_df.loc[:,force_cols])
        print(force_score)
        print(f"Average force error on the {datapoints} " +
            f"test datapoints: {force_score} N")
        if log_to_wandb: wandb.log({"force_test_score": force_score})

    cop_cols = [col for col in target_cols if 'C' in col]
    if len(cop_cols) > 0:
        cop_score = score(pred_df.loc[:,cop_cols], true_df.loc[:,cop_cols])
        print(f"Average CoP error on the {datapoints} " +
            f"test datapoints: {cop_score} mm")
        wandb.log({"CoP_test_score": cop_score})

    base_index = [
        'Unit',
        'Average Error',
        '(95% CI)',
        'RMSE',
        'Worst',
        'Full-scale Range',
        'Average Error, %FS',
        '(95% CI), %FS',
        'RMSE, %FS',
        'Worst, %FS',
        'MAPE',
        '% APE < 5',
        '% APE < 1',
        'R2 Score',
        'MAPE Threshold'
    ]

    if interval_mode: 
        base_index += ['Interval coverage', 'Mean interval width']

    metrics_df = pd.DataFrame(index=base_index)

    orig_axes = globals()['_TARGET_ORDER']      # 5 model output axes
    for col in target_cols:           # now includes Cx,Cy
        is_orig = col in orig_axes    # True for Fx,Fy,Fz, etc.

        if interval_mode and is_orig:
            j = orig_axes.index(col)      # column within L_pred/U_pred
            L_col  = L_pred[:, j]
            U_col  = U_pred[:, j]
            cov_ax = interval_cov_dict[col]
            wid_ax = interval_width_dict[col]
        else:                             # derived axis → no interval info
            L_col = U_col = cov_ax = wid_ax = None

        analyse_target_perf(
            pred_df[col], true_df[col], col,
            Y_CoPs, Y_forces, metrics_df,
            plate_size, log_to_wandb, plot,
            mape_threshold=50,
            L_bound=L_col, U_bound=U_col,
            interval_mode=interval_mode and is_orig,
            cov_val=cov_ax, width_val=wid_ax
        )

    base_save_path = base_save_path if base_save_path is not None else ""
    metrics_df.to_csv(base_save_path + "_results-summary.csv")
    if log_to_wandb:
        wandb.save(base_save_path + "_results-summary.csv")

def absolute_percentage_error(targets, predictions):
    epsilon = np.finfo(np.float64).eps
    return np.abs(predictions - targets) / np.maximum(np.abs(targets), epsilon)

def analyse_target_perf(predictions, targets, name,
                        Y_CoPs, Y_forces, metrics_df,
                        plate_size=(500,500), log_to_wandb=True,
                        plot=False, mape_threshold=100,
                        L_bound=None, U_bound=None, interval_mode=False,
                        cov_val=None, width_val=None):
    '''Single column performance analysis'''
    plate_width_x = plate_size[0]
    plate_width_y = plate_size[1]
    unit = ''
    if name in ['Fx', 'Fy', 'Fz']:
        unit = 'N'
    elif name in ['Cx', 'Cy']:
        unit = 'mm'

    predictions_mape_thresholded = predictions.where(abs(targets) > mape_threshold, np.nan).dropna()
    targets_mape_thresholded = targets.where(abs(targets) > mape_threshold, np.nan).dropna()

    print(f"Performance analysis for {name}")
    print("")
    errors = np.abs(predictions - targets)
    ave_error = np.mean(errors)
    min_error = np.min(errors)
    max_error = np.max(errors)
    sdev_error = np.std(errors)
    q1_error = np.quantile(errors, 0.25)
    q3_error = np.quantile(errors, 0.75)
    median_error = np.median(errors)
    ci95_error = 1.96 * sdev_error
    rmse = np.sqrt(mean_squared_error(targets, predictions))
    r2 = r2_score(targets, predictions)

    mape = mean_absolute_percentage_error(targets_mape_thresholded, predictions_mape_thresholded) * 100
    apes = absolute_percentage_error(targets_mape_thresholded, predictions_mape_thresholded) * 100
    apes_under_5_count = len([ape for ape in apes if ape < 5])
    apes_under_1_count = len([ape for ape in apes if ape < 1])
    proportion_ape_under_5 = apes_under_5_count / len(apes)
    proportion_ape_under_1 = apes_under_1_count / len(apes)

    maxval = np.max(targets)
    minval = np.min(targets)
    if name == 'Cx': fs = plate_width_x
    elif name == 'Cy': fs = plate_width_y
    else: fs = maxval - minval if maxval != minval else maxval
    ave_error_percent_fullscale = (ave_error / fs) * 100
    min_error_percent_fullscale = (min_error / fs) * 100
    max_error_percent_fullscale = (max_error / fs) * 100
    sdev_error_percent_fullscale = (sdev_error / fs) * 100
    q1_error_percent_fullscale = (q1_error / fs) * 100
    q3_error_percent_fullscale = (q3_error / fs) * 100
    median_error_percent_fullscale = (median_error / fs) * 100
    ci95_error_percent_fullscale = (ci95_error / fs) * 100

    # --- 15 standard entries --------------------------------------
    col_values = [
        unit,
        f'{ave_error:.2f}',
        f'{ci95_error:.2f}',
        f'{rmse:.2f}',
        f'{max_error:.2f}',
        f'{fs:.2f}',
        f'{ave_error_percent_fullscale:.2f}',
        f'{ci95_error_percent_fullscale:.2f}',
        f'{(rmse/fs)*100:.2f}',
        f'{(max_error/fs)*100:.2f}',
        f'{mape:.2f}',
        f'{proportion_ape_under_5*100:.2f}',
        f'{proportion_ape_under_1*100:.2f}',
        f'{r2:.5f}',
        f'{mape_threshold}'
    ]

    # --- append placeholders if interval rows exist ---------------
    if interval_mode:
        col_values += ["", ""]          # two empty slots for now

    metrics_df[name] = col_values

    print(f"Max {name}: {fs:.2f} {unit}")
    print(f"Min {name}: {fs:.2f} {unit}")
    print(f"Full-scale: {fs:.2f} {unit}")
    print(f"Average {name} error (95% CI): {ave_error:.2f} ({ci95_error:.2f}) {unit}")
    print(f"Average {name} error (95% CI) as % of full-scale: {ave_error_percent_fullscale:.2f} ({ci95_error_percent_fullscale:.2f})%")
    print(f"Standard deviation of {name} error: {sdev_error:.2f} {unit}")
    print(f"Standard deviation of {name} error as % of full-scale: {sdev_error_percent_fullscale:.2f}%")
    print(f"RMSE of {name}: {rmse:.2f} {unit}")
    print(f"RMSE of {name} as % of full-scale: {(rmse/fs)*100:.2f}%")
    print(f"MAPE {name} (threshold {mape_threshold}): {mape:.2f}%")
    print(f"Proportion with MAPE {name} (threshold {mape_threshold}) < 5%: {proportion_ape_under_5*100}%")
    print(f"Proportion with MAPE {name} (threshold {mape_threshold}) < 1%: {proportion_ape_under_1*100}%")
    print(f"R2 score of {name}: {r2:.5f}")
    print(f"{name} error quantiles in {unit}: {min_error:.2f}, {q1_error:.2f}, {median_error:.2f}, {q3_error:.2f}, {max_error:.2f}")
    print(f"{name} error quantiles as % of full-scale: {min_error_percent_fullscale:.2f}, {q1_error_percent_fullscale:.2f}, {median_error_percent_fullscale:.2f}, {q3_error_percent_fullscale:.2f}, {max_error_percent_fullscale:.2f}")

    if interval_mode and cov_val is not None:
        metrics_df.loc['Interval coverage',   name] = f'{cov_val*100:.2f}%'
        metrics_df.loc['Mean interval width', name] = (
            f'{width_val:.2f} {unit} | {width_val/fs*100:.2f}% FS')

    # if interval_mode and L_bound is not None:
    #     coverage = np.mean((targets >= L_bound) & (targets <= U_bound))
    #     width    = np.mean(U_bound - L_bound)

    #     metrics_df.loc['Interval coverage',    name] = f'{coverage*100:.2f}%'
    #     metrics_df.loc['Mean interval width',  name] = (
    #         f'{width:.2f} {unit}  |  {width/fs*100:.2f}% FS'
    #     )

    if log_to_wandb:
        wandb.log({f"{name.lower()}_test_error_%fullscale": ave_error_percent_fullscale})
        wandb.log({f"{name.lower()}_test_error_%fullscale_ci95": ci95_error_percent_fullscale})
        wandb.log({f"{name.lower()}_test_error_%fullscale_rmse": (rmse/fs)*100})
        wandb.log({f"{name.lower()}_test_error_%fullscale_worst": (max_error/fs)*100})
        wandb.log({f"{name.lower()}_test_error_%fullscale_q1": (q1_error/fs)*100})
        wandb.log({f"{name.lower()}_test_error_%fullscale_q3": (q3_error/fs)*100})
        wandb.log({f"{name.lower()}_test_error_%fullscale_median": (median_error/fs)*100})
        wandb.log({f"{name.lower()}_test_error_%fullscale_sdev": (sdev_error/fs)*100})
        wandb.log({f"{name.lower()}_test_error_{unit}": ave_error})
        wandb.log({f"{name.lower()}_test_error_{unit}_ci95": ci95_error})
        wandb.log({f"{name.lower()}_test_error_{unit}_rmse": rmse})
        wandb.log({f"{name.lower()}_test_error_{unit}_worst": max_error})
        wandb.log({f"{name.lower()}_test_error_{unit}_q1": q1_error})
        wandb.log({f"{name.lower()}_test_error_{unit}_q3": q3_error})
        wandb.log({f"{name.lower()}_test_error_{unit}_median": median_error})
        wandb.log({f"{name.lower()}_test_error_{unit}_sdev": sdev_error})
        wandb.log({f"{name.lower()}_test_error_{unit}_r2": r2})
        wandb.log({f"{name.lower()}_test_error_mape": mape})
        wandb.log({f"{name.lower()}_test_error_%ape_under_5": proportion_ape_under_5})
        wandb.log({f"{name.lower()}_test_error_%ape_under_1": proportion_ape_under_1})

    error_percents_fs = (np.abs(targets - predictions) / fs) * 100
    error_percents_load = np.abs((targets - predictions) / targets) * 100

    if plot:
        # limit number of points to plot to 10000
        cutoff = 10000
        if len(targets) > cutoff:
            targets = targets[0:cutoff]
            predictions = predictions[0:cutoff]
            error_percents_fs = error_percents_fs[0:cutoff]
            error_percents_load = error_percents_load[0:cutoff]
            Y_CoPs = Y_CoPs[0:cutoff]
            Y_forces = Y_forces[0:cutoff]
            if interval_mode and L_bound is not None:
                L_bound = L_bound[:cutoff]
                U_bound = U_bound[:cutoff]

        # Plot an error heatmap to see if error is related to CoP
        fig = px.density_heatmap(data_frame=Y_CoPs, x='Cx', y='Cy', z=error_percents_fs, histfunc="avg",
            width=400*(plate_width_x/500)+100, height=400,
            range_x=(-plate_width_x/2,plate_width_x/2), range_y=(-plate_width_y/2,plate_width_y/2),
            labels={
                    'Cx': f"X Centre of Pressure ({unit})",
                    'Cy': f"Y Centre of Pressure ({unit})",
                    'z': "Error",
                    },
            title=f"Avg {name} Error (% of FS) vs CoP")

        fig.update_traces(xbins=dict(start=-plate_width_x/2, end=plate_width_x/2, size=15), selector=dict(type='histogram2d'))
        fig.update_traces(ybins=dict(start=-plate_width_y/2, end=plate_width_y/2, size=15), selector=dict(type='histogram2d'))
        fig.update_layout(coloraxis_cmin=0, coloraxis_cmax=1)
        py_offline.plot(fig)
        # wandb.log({f"Avg {name} Error (% of FS) vs CoP": fig})

        df = pd.DataFrame({'Actual': targets, 'Predicted': predictions})
        fig = px.line(df,
                      labels={
                          'value': f"{name} ({unit})",
                            'variable': "Type",
                            'index': "Sample",
                            },
                        title=f"Actual vs Predicted {name}")
        # fig.show()
        py_offline.plot(fig)

        fig = px.scatter(x=targets, y=predictions,
            width=400, height=400,
            labels={
                    'x': f"Actual {name} ({unit})",
                    'y': f"Predicted {name} ({unit})",
                    },
            title=f"Actual vs Predicted {name}")
        fig.update_xaxes(scaleratio=1)
        # fig.show()
        py_offline.plot(fig)

        # # reduce the number of points to plot
        # spacing = 1
        # targets = targets[::spacing]
        # predictions = predictions[::spacing]

        fig = px.histogram(x=targets, y=error_percents_fs, histfunc="avg",
            width=800, height=600,
            labels={
                    'x': f"True {name} ({unit})",
                    'y': f"Avg {name} Error (% of FS)",
                    },
            title=f"Avg {name} Error (% of FS) vs True Value")
        py_offline.plot(fig)

        fig = px.histogram(x=targets, y=error_percents_load, histfunc="avg",
            width=800, height=600,
            labels={
                    'x': f"True {name} ({unit})",
                    'y': f"Avg {name} Error (% of Load)",
                    },
            title=f"Avg {name} Error (% of Load) vs True Value")
        py_offline.plot(fig)

        # fig = px.histogram(x=Y_forces['Fz'], y=error_percents_fs, histfunc="avg",
        #     width=800, height=600,
        #     labels={
        #             'x': f"Fz (N)",
        #             'y': f"Avg {name} Error (% of FS)",
        #             },
        #     title=f"Avg {name} Error (% of FS) vs Fz")
        # fig.show()

        # ───────── interval-specific plots (one target) ─────────
        if interval_mode and L_bound is not None:
            import plotly.graph_objects as go

            target_order = globals().get('_TARGET_ORDER', [])
            if name in target_order:
                col_idx = target_order.index(name)

                # (a) time-series ribbon
                idx = np.arange(len(targets))
                fig_ts = go.Figure()
                fig_ts.add_trace(go.Scatter(
                    x=idx, y=targets, name='Actual',
                    mode='lines', line=dict(width=2)))
                fig_ts.add_trace(go.Scatter(
                    x=idx, y=predictions, name='Predicted (mid)',
                    mode='lines', line=dict(width=1, dash='dot')))
                fig_ts.add_trace(go.Scatter(
                    x=np.concatenate([idx, idx[::-1]]),
                    y=np.concatenate([L_bound, U_bound[::-1]]),
                    fill='toself', fillcolor='rgba(0,100,200,0.15)',
                    line=dict(color='rgba(255,255,255,0)'),
                    hoverinfo='skip', name='95 % PI'))
                fig_ts.update_layout(
                    title=f"{name} with 95 % Prediction Interval",
                    xaxis_title="Sample", yaxis_title=f"{name} ({unit})")
                py_offline.plot(fig_ts)

                # (b) scatter with vertical error bars
                err_minus = predictions - L_bound
                err_plus  = U_bound - predictions

                import plotly.graph_objects as go
                fig_sc = go.Figure()

                # 1. spread markers + error bars  (semi-transparent grey)
                fig_sc.add_trace(go.Scatter(
                    x=targets, y=predictions,
                    mode='markers',
                    marker=dict(color='rgba(150,150,150,0.4)', size=5),
                    error_y=dict(type='data',
                                array=err_plus,
                                arrayminus=err_minus,
                                thickness=1,
                                color='rgba(150,150,150,0.4)'),
                    name='Prediction interval'
                ))

                # 2. centre points  (red), added LAST → plotted on top
                fig_sc.add_trace(go.Scatter(
                    x=targets, y=predictions,
                    mode='markers',
                    marker=dict(color='red', size=4),
                    name='Mid-prediction'
                ))

                fig_sc.update_xaxes(scaleanchor="y", scaleratio=1)
                fig_sc.update_layout(
                    title=f"Actual vs Predicted {name} with 95 % PI",
                    xaxis_title=f"Actual {name}",
                    yaxis_title=f"Predicted {name} (mid)"
                )
                py_offline.plot(fig_sc)

                # (c) CoP heat-map of PI width (%FS)
                width_pct = (U_bound - L_bound) / fs * 100
                fig_hm = px.density_heatmap(
                    x=Y_CoPs['Cx'], y=Y_CoPs['Cy'], z=width_pct,
                    histfunc="avg",
                    nbinsx=int(plate_width_x/15), nbinsy=int(plate_width_y/15),
                    range_x=(-plate_width_x/2, plate_width_x/2),
                    range_y=(-plate_width_y/2, plate_width_y/2),
                    labels={'x':"Cx (mm)", 'y':"Cy (mm)", 'z':"PI width %FS"},
                    title=f"Mean 95 % PI Width (%FS) vs CoP - {name}")
                py_offline.plot(fig_hm)
        # ──────────────────────────────────────────────────────────

    # px.scatter(data_frame=Y_CoPs, x='Cx', y='Cy', color=Fx_errors, width=600, height=600,
    #    range_x=(-225,225), range_y=(-225,225),
    # #    range_y=(100,500), range_x=(400,800),
    #    labels={
    #         'Cx': "X Centre of Pressure (mm)",
    #         'Cy': "Y Centre of Pressure (mm)",
    #         'color': "Error (N)",
    #         },
    #    title="X-Direction Force Error vs Centre of Pressure").show()

    # px.scatter_3d(data_frame=Y_CoPs, x='Cx', y='Cy', z=Fx_errors, color=Fx_errors, width=800, height=800,
    #    range_x=(-225,225), range_y=(-225,225),
    # #    range_y=(100,500), range_x=(400,800),
    #    labels={
    #         'Cx': "X Centre of Pressure (mm)",
    #         'Cy': "Y Centre of Pressure (mm)",
    #         'z': "Error (N)",
    #         'color': "Error (N)",
    #         },
    #    title="X-Direction Force Error vs Centre of Pressure").show()

def get_num_inputs(model):
    if issubclass(type(model), torch.nn.Module):
        first_child = next(model.children())
        while first_child is not None:
            if isinstance(first_child, torch.nn.LSTM):
                return first_child.input_size
            elif hasattr(first_child, 'in_features'):
                return first_child.in_features
            first_child = next(first_child.children(), None)
    else:
        return model.n_features_in_
    raise ValueError("No valid input size attribute found in model's first layer.")

class SklearnWrapper(torch.nn.Module):
    """
    A simple wrapper to call a scikit-learn estimator's .predict()
    from within a PyTorch forward pass.
    """
    def __init__(self, estimator):
        super(SklearnWrapper, self).__init__()
        self.estimator = estimator

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x_np = x.detach().cpu().numpy()
        # Perform prediction using the sklearn model
        y_np = self.estimator.predict(x_np)

        # Ensure y_np is 2D if you have multiple outputs
        # (some sklearn regressors return 1D even if there's 1 output target).
        if len(y_np.shape) == 1:
            y_np = y_np[:, np.newaxis]

        # Convert back to torch
        return torch.from_numpy(y_np).float()

def fuse_scalers_into_sgd(mo: MultiOutputRegressor,
                          x_scaler, y_scaler) -> SGDRegressor:
    """
    Returns a *single* SGDRegressor whose coefficients already include
    the effects of x_scaler (input) and y_scaler (output).

    Assumes:
    - mo.estimator is an SGDRegressor
    - x_scaler, y_scaler are fitted StandardScaler instances
    - both scalers use `with_mean=True, with_std=True`
    """
    # sanity checks
    assert isinstance(mo, MultiOutputRegressor)
    assert isinstance(mo.estimator, SGDRegressor)
    coef_     = np.vstack([est.coef_     for est in mo.estimators_])  # shape (n_outputs, n_features)
    intercept = np.array([est.intercept_ for est in mo.estimators_]).ravel()

    # 1. embed input scaling  x' = (x - μx) / σx
    coef_ = coef_ / x_scaler.scale_      # divide each column by σx
    intercept -= coef_.dot(x_scaler.mean_)

    # 2.undo output scaling  y = ŷ * σy + μy
    coef_ = (coef_.T * y_scaler.scale_).T   # multiply each row by σy
    intercept = intercept * y_scaler.scale_ + y_scaler.mean_

    # 3. build a fresh single-head SGDRegressor
    fused = SGDRegressor()
    fused.coef_      = coef_
    fused.intercept_ = intercept
    fused.n_features_in_ = coef_.shape[1]
    fused.n_iter_    = 1          # anything >0 tricks predict() into working
    fused.t_         = 1
    return fused

def combine_model_and_scalers(model, x_scaler, y_scaler, num_inputs, num_outputs):
    # Convert StandardScalers to pytorch LinearTransforms to include in ONNX model
    input_mean = torch.tensor(x_scaler.mean_, dtype=torch.float32)
    input_std = torch.tensor(x_scaler.scale_, dtype=torch.float32)
    output_mean = torch.tensor(y_scaler.mean_, dtype=torch.float32)
    output_std = torch.tensor(y_scaler.scale_, dtype=torch.float32)

    input_scaling = torch.nn.Linear(in_features=num_inputs, out_features=num_inputs)
    input_scaling.weight = torch.nn.Parameter(torch.diag(1/input_std), requires_grad=False)
    input_scaling.bias = torch.nn.Parameter(-input_mean/input_std, requires_grad=False)

    # discover how many features the estimator really outputs
    with torch.no_grad():
        probe = torch.randn(1, num_inputs)
        probe_out = model(probe)
    out_dim = probe_out.shape[1]             # x  or  2x (interval)

    # duplicate y_scaler stats if interval
    if out_dim == len(y_scaler.mean_) * 2:   # interval case
        out_mean = np.tile(y_scaler.mean_, 2)
        out_std  = np.tile(y_scaler.scale_, 2)
    else:                                     # ordinary case
        out_mean = y_scaler.mean_
        out_std  = y_scaler.scale_

    output_mean = torch.tensor(out_mean, dtype=torch.float32)
    output_std  = torch.tensor(out_std,  dtype=torch.float32)

    output_scaling = torch.nn.Linear(out_dim, out_dim)
    output_scaling.weight = torch.nn.Parameter(torch.diag(output_std), requires_grad=False)
    output_scaling.bias   = torch.nn.Parameter(output_mean, requires_grad=False)

    # Check if model is a PyTorch nn.Module
    if isinstance(model, torch.nn.Module):
        # Create a new Sequential model with the input scaling, original model, and output scaling
        combined_model = torch.nn.Sequential(OrderedDict([
            ('input_scaler',  input_scaling),
            ('estimator',     model),
            ('output_scaler', output_scaling),
        ]))
        combined_model.out_dim = out_dim
        return combined_model
    # Check if model is sklearn-like (has .predict() and is a BaseEstimator)
    elif isinstance(model, BaseEstimator) and hasattr(model, "predict"):
        combined_model = fuse_scalers_into_sgd(model, x_scaler, y_scaler)
        return combined_model
    else:
        raise ValueError(f"Unsupported model type: {type(model)}. "
                         "Expected a torch.nn.Module or an sklearn-style estimator.")

class PyTorchDataReader(CalibrationDataReader):
    def __init__(self, data_loader, scale_in_model, target_names, input_scaler=None, output_scaler=None):
        """ONNX-friendly data reader for PyTorch DataLoader."""
        self.data_loader = data_loader
        self.data_iter = iter(self.data_loader)
        self.input_name = 'loadcells'
        self.output_name = target_names
        self.scale_in_model = scale_in_model
        self.input_scaler = input_scaler
        self.output_scaler = output_scaler

    def get_next(self):
        try:
            inputs, outputs = next(self.data_iter)
        except StopIteration:
            return None

        # Invert scaling if scaling is done in the model
        if self.scale_in_model:
            if self.input_scaler:
                if inputs.dim() == 3:
                    # Reshape to 2D for inverse scaling
                    batch_size, seq_len, num_features = inputs.shape
                    inputs = inputs.view(-1, num_features)
                    inputs = torch.Tensor(self.input_scaler.inverse_transform(inputs.cpu().numpy()))
                    inputs = inputs.view(batch_size, seq_len, num_features)
                elif inputs.dim() == 2:
                    inputs = torch.Tensor(self.input_scaler.inverse_transform(inputs.cpu().numpy()))
            if self.output_scaler:
                outputs = torch.Tensor(self.output_scaler.inverse_transform(outputs.cpu().numpy()))

        # Adjust dimensions for ONNX input requirements
        if inputs.dim() == 3:
            inputs = inputs.view(-1, inputs.size(-1))  # Flatten to 2D

        # Convert to PyTorch tensors
        inputs = torch.Tensor(inputs)
        outputs = torch.Tensor(outputs)

        return {self.input_name: inputs.cpu().numpy()}

    def rewind(self):
        self.data_iter = iter(self.data_loader)

def quantize_model_and_save(model, x_scaler: StandardScaler, y_scaler: StandardScaler, target_names, base_name, train_loader, scale_in_model):
    """Quantize the model for use on edge devices"""
    quant_pre_process(base_name + '.onnx', base_name + '_preprocessed.onnx')
    target_name = "_".join(target_names)

    train_loader.dataset.x.cpu()
    train_loader.dataset.y.cpu()

    # Modify the DataLoader to use batch size of 1 if the input is 3D
    if train_loader.dataset.x.ndim == 3:
        single_batch_loader = DataLoader(train_loader.dataset, batch_size=1, shuffle=False)
    else:
        single_batch_loader = train_loader

    train_data_reader = PyTorchDataReader(
        single_batch_loader,
        scale_in_model=scale_in_model,
        target_names=target_name,
        input_scaler=x_scaler,
        output_scaler=y_scaler
    )

    conf = StaticQuantConfig(
        calibration_data_reader=train_data_reader,
        quant_format=QuantFormat.QDQ,
        calibrate_method=CalibrationMethod.MinMax,
        # optimize_model=True,
        activation_type=QuantType.QInt8,
        weight_type=QuantType.QInt8,
        per_channel=True,
        nodes_to_exclude=['loadcells', '/input_scaler/Gemm', '/estimator/layers/layers.0/Gemm', '/estimator/layers/layers.4/Gemm', '/output_scaler/Gemm', target_name]
    )

    quantized_model = ort_quantize(
        base_name + '_preprocessed.onnx',
        base_name + '_quantized.onnx',
        conf
    )
    return quantized_model

def save_model(model, x_scaler:StandardScaler, y_scaler:StandardScaler, num_inputs, target_names, base_name, sequence_length=1):
    # wandb.run.save()
    # num_outputs = len(target_names)
    # target_name = "_".join(target_names) # single output, so we make a composite name

    # detect real output width
    if hasattr(model, 'out_dim'):  # set by combine_model…
        out_dim = model.out_dim
    else:
        with torch.no_grad():
            probe = torch.randn(1, num_inputs)
            out_dim = model(probe).shape[1]

    interval_mode = (out_dim == 2 * len(target_names))

    # build output_names list
    if interval_mode:
        output_names = ([f"{t}_L" for t in target_names] +
                        [f"{t}_U" for t in target_names])
    else:
        output_names = target_names

    target_name = "_".join(output_names)

    # dynamic-axes dict for every output name
    # dyn_axes = {'loadcells': {0: 'batch_size'}}
    # dyn_axes.update({n: {0: 'batch_size'} for n in output_names})

    # check if model is a pytorch model or a scikit-learn model
    if issubclass(type(model), torch.nn.Module):
        # num_inputs = model.pretrained_model.layers[0].in_features if hasattr(model, 'pretrained_model') else model.children[0].in_features

        torch.save(model.state_dict(), base_name + '.pt')

        # Use individual output names
        # output_names = target_names
        device = next(model.parameters()).device
        model.to(device)

        # Differentiate between sequenced and non-sequenced models
        if any(isinstance(layer, torch.nn.LSTM) or isinstance(layer, torch.nn.GRU) for layer in model.modules()):
            dummy_input = torch.randn(sequence_length, num_inputs).to(device)  # 3D input for LSTM/GRU
        else:
            dummy_input = torch.randn(1, num_inputs).to(device)  # 2D input for non-sequenced models

        # Export the model with a single output name
        torch.onnx.export(
            model.cpu(),
            dummy_input.cpu(),
            base_name + '.onnx',
            input_names=['loadcells'],
            output_names=[target_name],
            dynamic_axes={'loadcells': {0: 'batch_size'},
                          target_name: {0: 'batch_size'}},
            opset_version=13,           # Explicitly pick a modern opset
            # do_constant_folding=False,  # Disable constant folding
            # verbose=True              # (optional) see debug info
        )

        wandb.save(base_name + '.onnx')
    else:
        joblib.dump(model, base_name + '.skl')

        if not (type(model) == NGBRegressor or type(model) == LGBMRegressor): # http://onnx.ai/sklearn-onnx/auto_tutorial/plot_gexternal_lightgbm_reg.html
            from skl2onnx import convert_sklearn
            from skl2onnx.common.data_types import FloatTensorType
            initial_type = [('loadcells', FloatTensorType([None, num_inputs]))]
            final_type = [(n, FloatTensorType([None, out_dim])) for n in output_names]
            onx = convert_sklearn(model, initial_types=initial_type, final_types=final_type)
            with open(base_name + '.onnx', "wb") as f:
                f.write(onx.SerializeToString())

    joblib.dump(x_scaler, base_name + '.xscl')
    joblib.dump(y_scaler, base_name + '.yscl')

def plot_losses(epochs, train_losses, validation_losses):
#   plt.plot(range(epochs), train_losses)
#   plt.plot(range(epochs), validation_losses)
#   plt.legend(["Train", "Validation"])
#   plt.ylabel('Loss')
#   plt.xlabel('epoch');

    losses = pd.DataFrame(np.transpose(np.array([train_losses, validation_losses])), columns=["train", "val"])
    losses = losses.reset_index()

    loss_df_melt = losses.melt(id_vars='index', value_vars=["train", "val"])
    fig = px.line(data_frame=loss_df_melt, x='index', y='value', width=1000, height=500 , color='variable',
                labels={
                    'index': "Epoch",
                    'value': "Loss"
                    },
                title="Training Losses: Training vs Validation Set")

    # fig.show()
    py_offline.plot(fig)

class IntervalScoreLoss(torch.nn.Module):
    """
    Interval Score for a (1-alpha) two-sided prediction interval.
    y_pred = [L1 … Ld | U1 … Ud],  y_true = [y1 … yd].
    """
    def __init__(self, alpha: float = 0.05):
        super().__init__()
        self.penalty = 2.0 / alpha      # 40 for 95 %

    def forward(self, y_pred: torch.Tensor, y_true: torch.Tensor):
        d = y_true.size(1)
        L, U = y_pred[:, :d], y_pred[:, d:]

        width  = (U - L)
        below  = (L - y_true).clamp(min=0.0)
        above  = (y_true - U).clamp(min=0.0)

        iscore = width + self.penalty * (below + above)
        return iscore.mean()
    
class PhysicsInformedLoss(torch.nn.Module):
    def __init__(self, codes_to_forces:np.ndarray, x_scaler:StandardScaler, y_scaler:StandardScaler):
        super(PhysicsInformedLoss, self).__init__()
        self.codes_to_forces = codes_to_forces
        self.x_scaler = x_scaler
        self.y_scaler = y_scaler
        self.mse_loss = torch.nn.MSELoss()

    def forward(self, Y_pred, Y, X):
        # Compute traditional MSE loss
        mse_loss_value = self.mse_loss(Y_pred, Y)

        # Reshape X to 2D array (batch_size, num_features)
        X = X.view(X.size(0), -1).cpu().detach().numpy()

        # Calculate the measured force (Fz component)
        X = self.x_scaler.inverse_transform(X)
        X *= self.codes_to_forces
        measured_force = X[:, [0, 1, 3, 5]].sum(axis=1, keepdims=True)

        measured_force_full = np.zeros((measured_force.shape[0], 5)) # fill out missing columns
        measured_force_full[:, 2] = measured_force[:, 0]

        measured_force_full = self.y_scaler.transform(measured_force_full)
        measured_force = measured_force_full[:, 2:3] # Extract Fz (3rd column)
        measured_force = torch.tensor(measured_force, dtype=torch.float32, device=Y_pred.device)

        Y_pred_Fz = Y_pred[:, 2:3]  # Fz is the 3rd output
        physics_loss_value = self.mse_loss(Y_pred_Fz, measured_force)

        # print(f"MSE Loss: {mse_loss_value.item()}, Physics Loss: {physics_loss_value.item()}")
        # print(f"Measured Force Scaled: {measured_force[0].item()}, Predicted Force Scaled: {Y_pred_Fz[0].item()}")
        # print(f"Measured Force: {self.y_scaler.inverse_transform([[0,0,measured_force.cpu().detach().numpy()[0][0],0,0]])}, Predicted Force: {self.y_scaler.inverse_transform([[0,0,Y_pred_Fz.cpu().detach().numpy()[0][0],0,0]])}")

        # Combine losses
        total_loss = 0.9 * mse_loss_value + 0.1 * physics_loss_value
        return total_loss

# Wrapper class to allow for the use of the PhysicsInformedLoss in the training loop alongside the normal loss functions
# Allows for an extra parameter to be passed to the normal loss functions
class LossWrapper(torch.nn.Module):
    def __init__(self, criterion:torch.nn.Module):
        super(LossWrapper, self).__init__()
        self.criterion = criterion

    def forward(self, Y_pred, Y, X):
        return self.criterion(Y_pred, Y)
