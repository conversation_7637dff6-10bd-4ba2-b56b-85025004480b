"""
Force Plate ML Configuration

This file contains configuration settings for the force plate machine learning models.
Separating configuration from logic makes it easier to track changes in the commit history.
"""

import math

# Flag to determine if we're training or testing
training = True  # true if wanting to train and save a fresh model, false for testing
run_name = "different-bush-95"  # if testing, provide the name of a model you'd like to load
sweeping = False  # whether to use sweep configurations

# Common configuration shared between regressor and learner
common_config = dict(
    plate_serial = "O06.04",
    artifact_name = "obi-wan06.04:v7",
    plate_dim_x = 500,  # plate dimensions in mm
    plate_dim_y = 500,
    fz_analysis_threshold_value = 200,  # N, minimum load for inclusion in accuracy analysis numbers
    scale_in_model = True,  # scale data as part of the model or beforehand
    train_test_val_split = [0.8, 0.1, 0.1],
    set_type = "rig",
    dataset_fraction = 1,  # use every <dataset_fraction> samples from the set (have too much data from the rig)
    fz_threshold = False,  # for input / training
    fz_threshold_value = 10,
    zero_frame_percentage = 0,
)

# Standard regressor configuration (used when not sweeping)
regressor_config = dict(
    target_cols = ['Fx', 'Fy', 'Fz', 'Mx', 'My'],
    model = "SGD",
    loss = "MSE",
    metric = "All:MAPE",
    batch_size = 0,
)

# Standard NN configuration (used when not sweeping)
learner_config = dict(
    target_cols = ['Fx', 'Fy', 'Fz', 'Mx', 'My'],
    layers = [32,16],
    epochs = 10,
    type = "VANN_INT",  # KAN, VANN, PINN, FEANN, LSTM, RANN
    residual_learner = False,
    linear_model_epochs = 2,
    physics_informed_type = None,  #"PINN6", # simple sum, no input features
    engineered_features = [],  # zero_point, window, diff
    use_autoencoder = False,
    autoencoder_encoding_dim = 20,
    autoencoder_epochs = 2,
    autoencoder_learning_rate = 0.01,
    activation = "gelu",
    optimizer = "adamw",
    scheduler = "onecycle",
    loss = "IS",
    metric = "B:MSE(test)+MSE(val)",
    upper_learning_rate = 0.01,
    # lower_learning_rate = 0.02,
    batch_norm = False,
    batch_size = 1000,
    sequence_length = 0,  # 0 if not LSTM
    window_size = 100,
    window_stride = 0,
    input_dropout = 0,
    hidden_dropout = 0,
    num_workers = 0,
    zero_loadcells = False,
    limit_test_range = False,  # if true, remove values outside the range limits (±)
    range_limits = [1000, 1000, 5000],
    base_model = "polished-sweep-39",
    base_layers = [500, 750],
    fine_tune = False,
    files_to_ignore = ['PlateW12_2023-04-19_Run07.csv'],  #'Plate01_2023-05-08_Run02.csv','Plate01_2023-05-08_Run03.csv','Plate01_2023-05-08_Run08.csv','Plate01_2023-05-08_Run09.csv','Plate01_2023-05-08_Run10.csv','Plate01_2023-05-08_Run11.csv'], #'Plate03_2023-02-20_Run19.csv', 'Plate03_2023-02-20_Run20.csv'
    test_set_files = []  #'concrete bolted one foot -x.csv', 'concrete bolted one foot -y.csv', 'concrete bolted one foot +x.csv', 'concrete bolted one foot +y.csv', 'concrete bolted one foot mid 2.csv', 'concrete bolted one foot mid.csv', 'concrete bolted two feet mid.csv', 'concrete bolted two feet mid 2.csv'],#['Plate03_2023-02-21_Run01.csv', 'Plate03_2023-02-21_Run03.csv', 'Plate03_2023-02-21_Run04.csv']
)

# Neural network sweep configuration (used when sweeping)
learner_sweep_config = {
    'name': 'platev03.4-rig-set11: forces - 2 layers. KAN',
    'method': 'bayes',
    'metric': {
        'name': 'combined_val_test_loss',  #'test_loss', # validation_loss
        'goal': 'minimize'
        },
    'early_terminate': {
        'type': 'hyperband',
        'min_iter': 3,
        },
    'parameters': {
        'target_cols': {
            'value': ['Fx','Fy','Fz']
            },
        'layercount': {
            'value': 2
            # 'values': [1,2] # 0 - option to just train a new output layer, not add anything
            },
        'layer_1': {
            'distribution': 'q_log_uniform',  # log-uniformly spaced between 10 and 500
            'min': math.log(5),
            'max': math.log(100)
            },
        'activation_1': {
            'value': "relu"
            },
        'layer_2': {
            'distribution': 'q_log_uniform',  # log-uniformly spaced between 10 and 500
            'min': math.log(5),
            'max': math.log(100),
            },
        'fine_tune': {
            'value': False  #if true, allow autograd on existing layers
            },
        'base_model': {
            'value': 'sim_cosmic-sweep-34',
            },
        'base_layers': {
            'value': [92],
            },
        'input_dropout': {
            'value': 0
            },
        'hidden_dropout': {
            'value': 0
            },
        'batch_norm': {
            'value': False
            },
        'epochs': {
            'distribution': 'q_log_uniform',  # log-uniformly spaced between 20 and 1000
            'min': math.log(1),
            'max': math.log(50),
            },
        'optimizer': {
            'value': 'adamw'
            },
        'scheduler': {
            'value': 'onecycle'
            },
        'loss': {
            'value': 'MSE'
            },
        'metric': {
            'value': 'B:MSE(test)+MSE(val)'
            },
        'upper_learning_rate': {
            'distribution': 'log_uniform',
            'max': math.log(0.1),
            'min': math.log(0.001),
            },
        'batch_size': {
            'values': [100,1000,10000]
            },
        'num_workers': {
            'value': 0,
            },
        'dataset_fraction': {
            'value': 1,
            },
        'train_test_val_split': {
            'value': [0.8,0.1,0.1]
            },
        'artifact_name' : {
            'value': 'vader03.4:v10',
            },
        'type': {
            'value': 'KAN'
            },
        'physics_informed_type': {
            'value': None,
            },
        'engineered_features': {
            'value': []
            },
        'scale_in_model': {
            'value': True
            },
        'fz_threshold': {
            'value': False
            },
        'fz_threshold_value': {
            'value': 10
            },
        'limit_test_range': {
            'value': False
            },
        'range_limits': {
            'value': [750,750,5000]
            },
        'files_to_ignore': {
            'value': ['PlateW12_2023-04-19_Run07.csv']
            },
        'test_set_files': {
            'value': ['concrete bolted one foot -x.csv', 'concrete bolted one foot -y.csv', 'concrete bolted one foot +x.csv', 'concrete bolted one foot +y.csv', 'concrete bolted one foot mid 2.csv', 'concrete bolted one foot mid.csv', 'concrete bolted two feet mid.csv', 'concrete bolted two feet mid 2.csv']
            },
    }
}

# Regressor sweep configuration (used when sweeping)
regressor_sweep_config = {
    'name': 'regressor-sweep: SGD vs LightGBM',
    'method': 'bayes',
    'metric': {
        'name': 'mean_absolute_error',
        'goal': 'minimize'
        },
    'early_terminate': {
        'type': 'hyperband',
        'min_iter': 3,
        },
    'parameters': {
        'target_cols': {
            'value': ['Fx', 'Fy', 'Fz', 'Mx', 'My']
            },
        'model': {
            'values': ['SGD', 'LightGBM']
            },
        'loss': {
            'values': ['MSE', 'MAE']
            },
        'dataset_fraction': {
            'values': [1, 2, 5, 10]
            },
        'scale_in_model': {
            'value': True
            },
    }
}

# Create the combined configurations by merging the common config with specific configs
def get_config(config_type='learner'):
    """
    Get the appropriate configuration by merging common config with specific config

    Args:
        config_type: 'learner' for neural network config, 'regressor' for regression models

    Returns:
        Combined configuration dictionary
    """
    if config_type == 'learner':
        if sweeping:
            # For learner sweep, we need to extract the parameter values
            sweep_params = {k: v['value'] for k, v in learner_sweep_config['parameters'].items()
                           if isinstance(v, dict) and 'value' in v}
            return {**common_config, **learner_config, **sweep_params}
        else:
            return {**common_config, **learner_config}
    else:  # regressor
        if sweeping:
            # For regressor sweep, we need to extract the parameter values
            sweep_params = {k: v['value'] for k, v in regressor_sweep_config['parameters'].items()
                           if isinstance(v, dict) and 'value' in v}
            return {**common_config, **regressor_config, **sweep_params}
        else:
            return {**common_config, **regressor_config}

# Create the final configurations
nn_config = get_config('learner')
regressor_config = get_config('regressor')
sweep_config = learner_sweep_config  # For backward compatibility
